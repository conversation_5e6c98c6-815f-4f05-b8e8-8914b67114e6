#!/bin/bash

# P6Spy配置验证脚本
# 用于快速验证P6Spy是否正确配置

echo "=== P6Spy配置验证脚本 ==="
echo ""

# 检查当前目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 错误: 请在项目根目录下运行此脚本"
    exit 1
fi

echo "✅ 项目根目录检查通过"

# 检查pom.xml中的p6spy依赖
echo ""
echo "🔍 检查Maven依赖配置..."

if grep -q "p6spy" pom.xml; then
    echo "✅ 在pom.xml中找到p6spy依赖"
    
    # 检查是否在正确的profile中
    if grep -A 20 -B 5 "p6spy" pom.xml | grep -q "dev\|test"; then
        echo "✅ p6spy依赖正确配置在dev/test环境中"
    else
        echo "⚠️  警告: p6spy依赖可能未正确配置在环境profile中"
    fi
else
    echo "❌ 在pom.xml中未找到p6spy依赖"
fi

# 检查配置文件
echo ""
echo "🔍 检查P6Spy配置文件..."

config_files=("yl-commons/yl-commons-config/src/main/resources/spy.properties" 
              "yl-commons/yl-commons-config/src/main/resources/spy-dev.properties"
              "yl-commons/yl-commons-config/src/main/resources/spy-test.properties")

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ 找到配置文件: $file"
    else
        echo "❌ 缺少配置文件: $file"
    fi
done

# 检查JdbcDataSource修改
echo ""
echo "🔍 检查JdbcDataSource类修改..."

jdbc_source_file="yl-commons/yl-commons-config/src/main/java/com/tuowan/yeliao/commons/config/mybatis/JdbcDataSource.java"

if [ -f "$jdbc_source_file" ]; then
    if grep -q "p6spy" "$jdbc_source_file"; then
        echo "✅ JdbcDataSource已正确修改以支持p6spy"
    else
        echo "❌ JdbcDataSource未包含p6spy相关代码"
    fi
else
    echo "❌ 未找到JdbcDataSource文件"
fi

# 检查验证工具类
echo ""
echo "🔍 检查P6Spy验证工具类..."

validator_file="yl-commons/yl-commons-config/src/main/java/com/tuowan/yeliao/commons/config/mybatis/P6spyConfigValidator.java"

if [ -f "$validator_file" ]; then
    echo "✅ P6Spy验证工具类已创建"
else
    echo "❌ P6Spy验证工具类缺失"
fi

# 提供编译测试命令
echo ""
echo "🚀 建议的测试步骤:"
echo ""
echo "1. 编译开发环境:"
echo "   mvn clean compile -Pdev"
echo ""
echo "2. 编译测试环境:"
echo "   mvn clean compile -Ptest"
echo ""
echo "3. 编译生产环境 (不包含p6spy):"
echo "   mvn clean compile -Pprod"
echo ""
echo "4. 启动应用并查看日志中的SQL输出"
echo ""

# 检查Maven是否可用
if command -v mvn &> /dev/null; then
    echo "💡 检测到Maven，可以运行以下命令进行快速验证:"
    echo ""
    echo "验证dev环境依赖:"
    echo "mvn dependency:tree -Pdev | grep p6spy"
    echo ""
    echo "验证test环境依赖:"
    echo "mvn dependency:tree -Ptest | grep p6spy"
    echo ""
    echo "验证prod环境依赖 (应该没有p6spy):"
    echo "mvn dependency:tree -Pprod | grep p6spy"
else
    echo "⚠️  未检测到Maven命令，请手动验证依赖"
fi

echo ""
echo "=== 验证脚本完成 ==="
