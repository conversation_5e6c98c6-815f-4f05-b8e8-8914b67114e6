@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === P6Spy配置验证脚本 ===
echo.

REM 检查当前目录
if not exist "pom.xml" (
    echo ❌ 错误: 请在项目根目录下运行此脚本
    pause
    exit /b 1
)

echo ✅ 项目根目录检查通过

REM 检查pom.xml中的p6spy依赖
echo.
echo 🔍 检查Maven依赖配置...

findstr /i "p6spy" pom.xml >nul
if !errorlevel! equ 0 (
    echo ✅ 在pom.xml中找到p6spy依赖
    
    REM 检查是否在正确的profile中
    findstr /i /c:"p6spy" pom.xml | findstr /i /c:"dev\|test" >nul
    if !errorlevel! equ 0 (
        echo ✅ p6spy依赖正确配置在dev/test环境中
    ) else (
        echo ⚠️  警告: p6spy依赖可能未正确配置在环境profile中
    )
) else (
    echo ❌ 在pom.xml中未找到p6spy依赖
)

REM 检查配置文件
echo.
echo 🔍 检查P6Spy配置文件...

set "config_files=yl-commons\yl-commons-config\src\main\resources\spy.properties yl-commons\yl-commons-config\src\main\resources\spy-dev.properties yl-commons\yl-commons-config\src\main\resources\spy-test.properties"

for %%f in (%config_files%) do (
    if exist "%%f" (
        echo ✅ 找到配置文件: %%f
    ) else (
        echo ❌ 缺少配置文件: %%f
    )
)

REM 检查JdbcDataSource修改
echo.
echo 🔍 检查JdbcDataSource类修改...

set "jdbc_source_file=yl-commons\yl-commons-config\src\main\java\com\tuowan\yeliao\commons\config\mybatis\JdbcDataSource.java"

if exist "%jdbc_source_file%" (
    findstr /i "p6spy" "%jdbc_source_file%" >nul
    if !errorlevel! equ 0 (
        echo ✅ JdbcDataSource已正确修改以支持p6spy
    ) else (
        echo ❌ JdbcDataSource未包含p6spy相关代码
    )
) else (
    echo ❌ 未找到JdbcDataSource文件
)

REM 检查验证工具类
echo.
echo 🔍 检查P6Spy验证工具类...

set "validator_file=yl-commons\yl-commons-config\src\main\java\com\tuowan\yeliao\commons\config\mybatis\P6spyConfigValidator.java"

if exist "%validator_file%" (
    echo ✅ P6Spy验证工具类已创建
) else (
    echo ❌ P6Spy验证工具类缺失
)

REM 提供编译测试命令
echo.
echo 🚀 建议的测试步骤:
echo.
echo 1. 编译开发环境:
echo    mvn clean compile -Pdev
echo.
echo 2. 编译测试环境:
echo    mvn clean compile -Ptest
echo.
echo 3. 编译生产环境 (不包含p6spy):
echo    mvn clean compile -Pprod
echo.
echo 4. 启动应用并查看日志中的SQL输出
echo.

REM 检查Maven是否可用
where mvn >nul 2>&1
if !errorlevel! equ 0 (
    echo 💡 检测到Maven，可以运行以下命令进行快速验证:
    echo.
    echo 验证dev环境依赖:
    echo mvn dependency:tree -Pdev ^| findstr p6spy
    echo.
    echo 验证test环境依赖:
    echo mvn dependency:tree -Ptest ^| findstr p6spy
    echo.
    echo 验证prod环境依赖 (应该没有p6spy):
    echo mvn dependency:tree -Pprod ^| findstr p6spy
) else (
    echo ⚠️  未检测到Maven命令，请手动验证依赖
)

echo.
echo === 验证脚本完成 ===
pause
