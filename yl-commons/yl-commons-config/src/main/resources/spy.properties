#################################################################
# P6Spy Options File for xchat-server                          #
# 适用于dev和test环境的SQL监控配置                                #
# See documentation for detailed instructions                  #
# http://p6spy.readthedocs.io/en/latest/configandusage.html   #
#################################################################

#################################################################
# MODULES                                                       #
#################################################################
modulelist=com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory

#################################################################
# CORE (P6SPY) PROPERTIES                                      #
#################################################################

# A comma separated list of JDBC drivers to load and register.
# (default is empty)
#
# Note: This is normally only needed when using P6Spy in an 
# application server environment with a jndi data source or when
# using a driver class name that does not begin with the protocol
# in the URL.
#
# driverlist=

# for flushing per statement
# (default is false)
autoflush = true

# sets the date format using Java's SimpleDateFormat routine. 
# In case property is not set, milliseconds since 1970 (unix time) is used (default is empty)
dateformat=yyyy-MM-dd HH:mm:ss

# prints a stack trace for every statement logged
stacktrace=false
# if stacktrace=true, specifies the stack trace to print
stacktraceclass=

# determines if property file should be reloaded
# Please note: reload means forgetting all the previously set
# settings (even those set during runtime - via JMX)
# and starting with the clean table 
# (default is false)
reload=false

# determines how often should be reloaded in seconds
# (default is 60)
reloadinterval=60

# specifies the appender to use for logging
# Please note: reload means forgetting all the previously set
# settings (even those set during runtime - via JMX)
# and starting with the clean table 
# (only the properties read from the configuration file)
# (default is com.p6spy.engine.spy.appender.FileAppender)
appender=com.p6spy.engine.spy.appender.Slf4JLogger

# name of logfile to use, note Windows users should make sure to use forward slashes in their pathname (e:/test/spy.log) 
# (used for com.p6spy.engine.spy.appender.FileAppender only)
# (default is spy.log)
logfile=logs/spy.log

# append to the p6spy log file. if this is set to false the
# log file is truncated every time. (file logger only)
# (default is true)
append=true

# class to use for formatting log messages (default is: com.p6spy.engine.spy.appender.SingleLineFormat)
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat

# format that is used for logging of the date/time/... (has to be compatible with java.text.SimpleDateFormat)
# (default is dd-MMM-yy)
#databaseDialectDateFormat=dd-MMM-yy

# whether to expose options via JMX or not
# (default is true)
jmx=true

# if exposing options via jmx, what should be the prefix?
# jmx naming pattern: com.p6spy(.<jmxPrefix>)?:name=<optionsClassName>
# please note, if there is already such a name in use it would be unregistered first (the last registered wins)
# (default is none)
jmxPrefix=

# if set to true, the execution time will be measured in nanoseconds as opposed to milliseconds
# (default is false)
#useNanoTime=false

#################################################################
# DataSource replacement                                        #
#                                                               #
# Replace the real DataSource class in your application server #
# configuration with the name com.p6spy.engine.spy.P6DataSource#
# (that provides also connection pooling and xa support).      #
# then add the JNDI name and class name of the real            #
# DataSource here                                               #
#                                                               #
# Values set in this item cannot be reloaded using the         #
# reloadproperties variable. Once it is loaded, it remains     #
# in memory until the application is restarted.               #
#                                                               #
#################################################################
#realdatasource=/RealMySqlDS
#realdatasourceclass=com.mysql.jdbc.jdbc2.optional.MysqlDataSource

#################################################################
# DataSource properties                                         #
#                                                               #
# If you are using the DataSource support to intercept calls   #
# to a DataSource that does not use a connection pool, and you #
# are therefore using the realdatasource variable, you can use #
# the following properties to set up the connection pool.      #
# The example shown is for mysql, and shows 2 methods to       #
# register the real datasource, one commented out, and one not.#
#                                                               #
# The datasource properties are used to set the login timeout, #
# connection pool timeout, and other properties.               #
#                                                               #
#################################################################
#realdatasourceproperties=user;password

#################################################################
# JNDI DataSource lookup                                        #
#                                                               #
# If you are using the DataSource support outside of an app    #
# server, you will probably need to define the JNDI Context    #
# environment.                                                  #
#                                                               #
# If the P6Spy code will be executing inside an app server then#
# do not use these properties, and the DataSource lookup will  #
# use the naming context defined by the app server.            #
#                                                               #
# The two standard elements of the naming environment are      #
# jndicontextfactory and jndicontextproviderurl. If you need   #
# additional elements, use the jndicontextcustom property.     #
# You can define multiple properties in jndicontextcustom,     #
# in name value pairs. Separate the name and value with a      #
# semicolon, and separate the pairs with commas.               #
#                                                               #
# The example shown here is for a standalone program running on#
# a machine that is also running JBoss, so the JNDI context   #
# is configured for JBoss (3.0.4).                            #
#                                                               #
# (by default all these are empty)                             #
#################################################################
#jndicontextfactory=org.jnp.interfaces.NamingContextFactory
#jndicontextproviderurl=localhost:1099
#jndicontextcustom=java.naming.factory.url.pkgs;org.jboss.nameing:org.jnp.interfaces

#jndicontextfactory=com.ibm.websphere.naming.WsnInitialContextFactory
#jndicontextproviderurl=iiop://localhost:900

################################################################
# P6 LOGGING SPECIFIC PROPERTIES                               #
################################################################

# filter what is logged
# please note this is a precondition for usage of: include/exclude/sqlexpression
# (default is false)
#filter=false

# comma separated list of strings to include
# please note that special characters escaping (used in java) has to be done for the provided regular expression
# (default is empty)
#include = 
# comma separated list of strings to exclude
# (default is empty)
#exclude =

# sql expression to evaluate if using regex
# please note that special characters escaping (used in java) has to be done for the provided regular expression
# (default is empty)
#sqlexpression = 

#list of categories to exclude: error, info, batch, debug, statement,
#commit, rollback, result and resultset are valid values
# (default is info,debug,result,resultset,batch)
excludecategories=info,debug,result,batch

# Execution threshold applies to the standard logging of P6Spy.
# While the standard logging logs out every statement
# regardless of its execution time, this feature puts a time
# condition on that logging. Only statements that have taken
# longer than the time specified (in milliseconds) will be
# logged. This way it is possible to see only statements that
# have exceeded some high water mark.
# This time is reloadable.
#
# executionThreshold=0

################################################################
# P6 OUTAGE SPECIFIC PROPERTIES                               #
################################################################
# Outage Detection
#
# This feature detects long-running statements that may be indicative of
# a database outage problem. If this feature is turned on, it will log any
# statement that surpasses the configurable time boundary during its execution.
# When this feature is enabled, no other statements are logged except the long
# running statements. The interval property is the boundary time set in seconds.
# For example, if this is set to 2, then any statement requiring at least 2 
# seconds will be logged. Note that the same statement will continue to be logged
# for as long as it executes. So if the interval is set to 2, and the query takes
# 11 seconds, it will be logged 5 times (at the 2, 4, 6, 8, 10 second intervals).
#
# outagedetection=false|true
# outagedetectioninterval=integer value (seconds)

################################################################
# P6 OPTIONS SPECIFIC PROPERTIES                              #
################################################################
#shownestedconfig=false

# Custom log line format used ONLY IF logMessageFormat is set to com.p6spy.engine.spy.appender.CustomLineFormat
# Available placeholders:
#   %(connectionId)            the id of the connection
#   %(currentTime)             the current time expressing in milliseconds
#   %(executionTime)           the time in milliseconds that the operation took to complete
#   %(category)                the category of the operation
#   %(effectiveSql)            the SQL statement as submitted to the driver
#   %(effectiveSqlSingleLine)  the SQL statement as submitted to the driver, with all new lines removed
#   %(sql)                     the SQL statement with all bind variables replaced with actual values
#   %(sqlSingleLine)           the SQL statement with all bind variables replaced with actual values, with all new lines removed

# 统一的日志格式，适用于dev和test环境
customLogMessageFormat=[SQL] %(currentTime) | 执行时间: %(executionTime)ms | 连接: %(connectionId) | %(sqlSingleLine)
