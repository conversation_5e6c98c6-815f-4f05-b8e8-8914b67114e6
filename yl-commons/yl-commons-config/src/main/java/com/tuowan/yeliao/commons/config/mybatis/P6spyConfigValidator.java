package com.tuowan.yeliao.commons.config.mybatis;

import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * P6Spy配置验证工具类
 * 用于验证P6Spy是否正确配置和启用
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public class P6spyConfigValidator {

    private static final Logger log = LoggerFactory.getLogger(P6spyConfigValidator.class);

    /**
     * 验证P6Spy配置状态
     * 
     * @return 验证结果信息
     */
    public static String validateP6spyConfig() {
        StringBuilder result = new StringBuilder();
        result.append("=== P6Spy配置验证结果 ===\n");

        // 1. 检查当前环境
        String currentEnv = AppConfig.ENV.name();
        result.append("当前环境: ").append(currentEnv).append("\n");

        // 2. 检查是否为生产环境
        boolean isProdEnv = UnifiedConfig.isProdEnv();
        result.append("是否生产环境: ").append(isProdEnv).append("\n");

        if (isProdEnv) {
            result.append("✅ 生产环境下P6Spy已正确禁用\n");
            return result.toString();
        }

        // 3. 检查P6Spy类是否存在
        boolean p6spyAvailable = false;
        try {
            Class.forName("com.p6spy.engine.spy.P6SpyDriver");
            p6spyAvailable = true;
            result.append("✅ P6Spy依赖已正确引入\n");
        } catch (ClassNotFoundException e) {
            result.append("❌ P6Spy依赖未找到，请检查Maven profile配置\n");
        }

        if (!p6spyAvailable) {
            return result.toString();
        }

        // 4. 检查配置文件
        String envConfigFile = "spy-" + currentEnv.toLowerCase() + ".properties";
        String defaultConfigFile = "spy.properties";

        ClassLoader classLoader = P6spyConfigValidator.class.getClassLoader();
        
        if (classLoader.getResource(envConfigFile) != null) {
            result.append("✅ 找到环境特定配置文件: ").append(envConfigFile).append("\n");
        } else if (classLoader.getResource(defaultConfigFile) != null) {
            result.append("⚠️  使用默认配置文件: ").append(defaultConfigFile).append("\n");
            result.append("   建议创建环境特定配置: ").append(envConfigFile).append("\n");
        } else {
            result.append("❌ 未找到P6Spy配置文件\n");
        }

        // 5. 检查系统属性
        String spyPropertiesPath = System.getProperty("spy.properties");
        if (spyPropertiesPath != null) {
            result.append("✅ P6Spy配置文件路径已设置: ").append(spyPropertiesPath).append("\n");
        } else {
            result.append("⚠️  P6Spy配置文件路径未设置，将使用默认配置\n");
        }

        result.append("=== 验证完成 ===\n");
        return result.toString();
    }

    /**
     * 打印P6Spy配置验证结果到日志
     */
    public static void logValidationResult() {
        String result = validateP6spyConfig();
        log.info("\n{}", result);
    }

    /**
     * 检查P6Spy是否已启用
     * 
     * @return true如果P6Spy已启用，false否则
     */
    public static boolean isP6spyEnabled() {
        if (UnifiedConfig.isProdEnv()) {
            return false;
        }

        try {
            Class.forName("com.p6spy.engine.spy.P6SpyDriver");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 获取当前使用的P6Spy配置文件名
     * 
     * @return 配置文件名，如果未启用则返回null
     */
    public static String getCurrentP6spyConfigFile() {
        if (!isP6spyEnabled()) {
            return null;
        }

        String envName = AppConfig.ENV.name().toLowerCase();
        String envConfigFile = "spy-" + envName + ".properties";
        String defaultConfigFile = "spy.properties";

        ClassLoader classLoader = P6spyConfigValidator.class.getClassLoader();

        if (classLoader.getResource(envConfigFile) != null) {
            return envConfigFile;
        } else if (classLoader.getResource(defaultConfigFile) != null) {
            return defaultConfigFile;
        }

        return null;
    }

    /**
     * 获取P6Spy状态摘要
     * 
     * @return 状态摘要字符串
     */
    public static String getP6spyStatusSummary() {
        if (UnifiedConfig.isProdEnv()) {
            return "P6Spy: 已禁用 (生产环境)";
        }

        if (!isP6spyEnabled()) {
            return "P6Spy: 未启用 (依赖缺失)";
        }

        String configFile = getCurrentP6spyConfigFile();
        if (configFile != null) {
            return "P6Spy: 已启用 (配置: " + configFile + ")";
        } else {
            return "P6Spy: 已启用 (无配置文件)";
        }
    }
}
