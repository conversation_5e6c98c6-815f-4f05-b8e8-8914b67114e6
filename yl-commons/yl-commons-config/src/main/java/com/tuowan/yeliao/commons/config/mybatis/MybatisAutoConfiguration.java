package com.tuowan.yeliao.commons.config.mybatis;

import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.sharding.routing.RoutingContextInterceptor;
import com.easyooo.framework.sharding.transaction.RoutingManagedTransactionFactory;
import com.easyooo.framework.support.mybatis.PaginationPlugin;
import com.easyooo.framework.support.mybatis.handler.DefaultEnumTypeHandler;
import com.tuowan.yeliao.commons.config.configuration.ServiceConfig;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.LocalCacheScope;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.aspectj.AspectJExpressionPointcutAdvisor;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.interceptor.*;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.*;

/**
 * 数据源配置
 *
 * <AUTHOR>
 */
@Configuration
public class MybatisAutoConfiguration implements ApplicationContextAware {

    private ApplicationContext context;

    @Bean
    public AspectJExpressionPointcutAdvisor pointcutAdvisor() {
        AspectJExpressionPointcutAdvisor advisor = new AspectJExpressionPointcutAdvisor();
        advisor.setExpression(ServiceConfig.DATA_PERSISTENCE_POINTCUT_EXP);
        advisor.setAdvice(new RoutingContextInterceptor());
        return advisor;
    }

    @Bean
    public MultiJdbcDataSource dataSource() {
        // 验证P6Spy配置状态
        P6spyConfigValidator.logValidationResult();
        return new MultiJdbcDataSource();
    }

    @Bean
    public RoutingManagedTransactionFactory transactionFactory() {
        return new RoutingManagedTransactionFactory();
    }

    /**
     * MapperScannerConfigurer需标记为static，以示优先加载，否则会给出警告
     *
     * @return
     */
    @Bean
    public static MapperScannerConfigurer mapperScannerConfigurer() {
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage(StringUtils.join(ServiceConfig.DATA_PERSISTENCE_PACKAGES, ","));
        configurer.setAnnotationClass(Repository.class);
        configurer.setSqlSessionFactoryBeanName("sqlSessionFactory");
        return configurer;
    }

    @Bean
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        RoutingManagedTransactionFactory transactionFactory = context.getBean(RoutingManagedTransactionFactory.class);
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(getMapperLocations());
        bean.setTypeAliasesPackage(ServiceConfig.ENTITY_BASE_PACKAGES);
        bean.setTypeHandlers(new TypeHandler[]{new DefaultEnumTypeHandler()});
        bean.setTransactionFactory(transactionFactory);
        bean.setPlugins(new Interceptor[]{context.getBean(PaginationPlugin.class)});

        SqlSessionFactory ssf = bean.getObject();
        ssf.getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
        ssf.getConfiguration().setMapUnderscoreToCamelCase(true);
        ssf.getConfiguration().setCacheEnabled(false);
        ssf.getConfiguration().setLocalCacheScope(LocalCacheScope.STATEMENT);
        return ssf;
    }

    @Bean
    public DistributedLockTransactionManager transactionManager() {
        return new DistributedLockTransactionManager();
    }

    @Bean
    public TransactionInterceptor transactionInterceptor() {
        /*只读事务，不做更新操作*/
        RuleBasedTransactionAttribute readOnlyTx = new RuleBasedTransactionAttribute();
        readOnlyTx.setReadOnly(true);
        readOnlyTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
        /*当前存在事务就使用当前事务，当前不存在事务就创建一个新的事务*/
        RuleBasedTransactionAttribute requiredTx = new RuleBasedTransactionAttribute();
        requiredTx.setRollbackRules(
                Collections.singletonList(new RollbackRuleAttribute(Throwable.class)));
        requiredTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        requiredTx.setReadOnly(false);
        Map<String, TransactionAttribute> txMap = new HashMap<>();
        txMap.put("add*", requiredTx);
        txMap.put("save*", requiredTx);
        txMap.put("edit*", requiredTx);
        txMap.put("insert*", requiredTx);
        txMap.put("update*", requiredTx);
        txMap.put("delete*", requiredTx);
        txMap.put("remove*", requiredTx);
        txMap.put("*", readOnlyTx);

        TransactionManager transMgr = context.getBean(DistributedLockTransactionManager.class);
        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        source.setNameMap(txMap);
        return new TransactionInterceptor(transMgr, source);
    }

    @Bean
    public Advisor txAdviceAdvisor() {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression(ServiceConfig.SERVICE_TRANS_EXP);

        TransactionInterceptor txAdvice = context.getBean(TransactionInterceptor.class);
        return new DefaultPointcutAdvisor(pointcut, txAdvice);
    }

    @Bean
    public PaginationPlugin paginationPlugin() {
        PaginationPlugin plugin = new PaginationPlugin();
        plugin.setDbms("mysql");
        return plugin;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }

    private Resource[] getMapperLocations() {
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        String[] locations = ServiceConfig.DATA_SQL_MAP_LOCATIONS;
        List<Resource> resourceList = new ArrayList<>();
        try {
            for (int i = 0; i < locations.length; i++) {
                Resource[] resources = resolver.getResources(locations[i]);
                for (Resource resource : resources) {
                    resourceList.add(resource);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resourceList.toArray(new Resource[0]);
    }
}
