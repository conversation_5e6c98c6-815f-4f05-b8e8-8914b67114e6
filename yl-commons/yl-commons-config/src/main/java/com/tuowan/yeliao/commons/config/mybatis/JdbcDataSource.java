package com.tuowan.yeliao.commons.config.mybatis;


import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.DataSourceConfig;
import com.tuowan.yeliao.commons.config.enums.DBType;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class JdbcDataSource extends HikariDataSource {

    private Logger log = LoggerFactory.getLogger(this.getClass());

    public JdbcDataSource(DBType dbType, DataSourceConfig config) {
        super();
        Properties props = config.getProps();
        String prefix = dbType.getPrefix();
        String originalJdbcUrl = PropUtils.getString(props, prefix, "jdbcUrlFcy");
        String originalDriverClassName = PropUtils.getString(props, prefix, "driverClassName");

        // 在非生产环境下使用p6spy包装数据源
        String jdbcUrl = originalJdbcUrl;
        String driverClassName = originalDriverClassName;

        if (!UnifiedConfig.isProdEnv()) {
            // 检查p6spy是否在classpath中
            try {
                Class.forName("com.p6spy.engine.spy.P6SpyDriver");
                // p6spy可用，使用p6spy包装
                jdbcUrl = "jdbc:p6spy:" + originalJdbcUrl.substring(5); // 移除原始的"jdbc:"前缀
                driverClassName = "com.p6spy.engine.spy.P6SpyDriver";

                log.info("Initialize {} database with p6spy：{}", dbType.getId(), jdbcUrl);
            } catch (ClassNotFoundException e) {
                // p6spy不可用，使用原始配置
                log.info("Initialize {} database：{}", dbType.getId(), jdbcUrl);
            }
        } else {
            log.info("Initialize {} database：{}", dbType.getId(), "****");
        }

        this.setSchema(dbType.getSchema());
        this.setPoolName(dbType.getId());
        this.setJdbcUrl(jdbcUrl);
        this.setUsername(PropUtils.getString(props, prefix, "username"));
        this.setPassword(PropUtils.getString(props, prefix, "password"));
        this.setMaximumPoolSize(PropUtils.getInt(props, prefix, "maximumPoolSize"));
        this.setMinimumIdle(PropUtils.getInt(props, prefix, "minimumIdle"));
        this.setConnectionTimeout(PropUtils.getInt(props, prefix, "connectionTimeout"));
        this.setDriverClassName(driverClassName);
        this.setConnectionInitSql(PropUtils.getString(props, prefix, "connectionInitSql"));
    }

    /**
     * 根据当前环境获取p6spy配置文件路径
     * @return p6spy配置文件路径，如果没有找到则返回null
     */
    private String getP6spyConfigFile() {
        try {
            String envName = AppConfig.ENV.name().toLowerCase();
            String configFileName = "spy-" + envName + ".properties";

            // 检查环境特定的配置文件是否存在
            if (getClass().getClassLoader().getResource(configFileName) != null) {
                return configFileName;
            }

            // 如果环境特定的配置文件不存在，检查默认配置文件
            if (getClass().getClassLoader().getResource("spy.properties") != null) {
                return "spy.properties";
            }

            return null;
        } catch (Exception e) {
            log.warn("Failed to determine p6spy config file, using default configuration", e);
            return null;
        }
    }
}
